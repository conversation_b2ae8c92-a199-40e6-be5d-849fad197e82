#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import fitz  # PyMuPDF
import qrcode
from PIL import Image
import io

def test_qr_positions():
    """Test nhiều vị trí QR code khác nhau"""
    
    # Mở file PDF gốc
    doc = fitz.open("Nhan hop.pdf")
    page = doc[0]
    
    # <PERSON><PERSON><PERSON> kích thước trang
    page_rect = page.rect
    print(f"Kích thước trang PDF: {page_rect.width} x {page_rect.height}")
    
    # Tạo QR code test
    qr = qrcode.QRCode(version=1, error_correction=qrcode.constants.ERROR_CORRECT_L, box_size=10, border=4)
    qr.add_data("TEST-QR")
    qr.make(fit=True)
    qr_img = qr.make_image(fill_color="black", back_color="white")
    qr_img = qr_img.resize((100, 100))
    
    # Chuyển PIL Image thành bytes
    img_buffer = io.BytesIO()
    qr_img.save(img_buffer, format='PNG')
    img_buffer.seek(0)
    
    # Test các vị trí khác nhau
    positions = [
        ("Góc trên trái", (50, 50, 150, 150)),
        ("Góc trên phải", (450, 50, 550, 150)),
        ("Góc dưới trái", (50, 650, 150, 750)),
        ("Góc dưới phải", (450, 650, 550, 750)),
        ("Giữa trang", (250, 350, 350, 450)),
        ("Bên phải giữa", (450, 350, 550, 450)),
    ]
    
    for i, (name, pos) in enumerate(positions):
        # Tạo bản sao của trang
        new_doc = fitz.open("Nhan hop.pdf")
        new_page = new_doc[0]
        
        # Chèn QR code
        rect = fitz.Rect(pos)
        new_page.insert_image(rect, stream=img_buffer.getvalue())
        
        # Thêm text mô tả vị trí
        new_page.insert_text((pos[0], pos[1] - 10), name, fontsize=8, color=(1, 0, 0))
        
        # Lưu file test
        output_file = f"test_qr_position_{i+1}_{name.replace(' ', '_')}.pdf"
        new_doc.save(output_file)
        new_doc.close()
        
        print(f"Đã tạo {output_file} với QR ở vị trí {name}: {pos}")
    
    doc.close()
    print("\nĐã tạo 6 file test với QR ở các vị trí khác nhau.")
    print("Hãy mở các file để xem vị trí nào phù hợp nhất!")

def create_custom_position(x1, y1, size=100):
    """Tạo file test với vị trí QR tùy chỉnh"""
    
    # Tính toạ độ x2, y2
    x2 = x1 + size
    y2 = y1 + size
    
    # Mở file PDF gốc
    doc = fitz.open("Nhan hop.pdf")
    page = doc[0]
    
    # Tạo QR code
    qr = qrcode.QRCode(version=1, error_correction=qrcode.constants.ERROR_CORRECT_L, box_size=10, border=4)
    qr.add_data("CUSTOM-QR")
    qr.make(fit=True)
    qr_img = qr.make_image(fill_color="black", back_color="white")
    qr_img = qr_img.resize((size, size))
    
    # Chuyển PIL Image thành bytes
    img_buffer = io.BytesIO()
    qr_img.save(img_buffer, format='PNG')
    img_buffer.seek(0)
    
    # Chèn QR code
    rect = fitz.Rect(x1, y1, x2, y2)
    page.insert_image(rect, stream=img_buffer.getvalue())
    
    # Lưu file
    output_file = f"test_qr_custom_{x1}_{y1}_{size}.pdf"
    doc.save(output_file)
    doc.close()
    
    print(f"Đã tạo {output_file} với QR tại vị trí ({x1}, {y1}) kích thước {size}x{size}")

def main():
    print("CÔNG CỤ ĐIỀU CHỈNH VỊ TRÍ QR CODE")
    print("=" * 40)
    
    choice = input("Chọn:\n1. Test 6 vị trí mẫu\n2. Tạo vị trí tùy chỉnh\nLựa chọn (1/2): ")
    
    if choice == "1":
        test_qr_positions()
    elif choice == "2":
        try:
            x1 = int(input("Nhập tọa độ X (từ trái): "))
            y1 = int(input("Nhập tọa độ Y (từ trên): "))
            size = int(input("Nhập kích thước QR (mặc định 100): ") or "100")
            create_custom_position(x1, y1, size)
        except ValueError:
            print("Vui lòng nhập số hợp lệ!")
    else:
        print("Lựa chọn không hợp lệ!")

if __name__ == "__main__":
    main()
