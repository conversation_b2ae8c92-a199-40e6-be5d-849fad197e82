#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script chính để chạy xử lý PDF
Chạy với: python run_processing.py
"""

try:
    from xu_ly_pdf import PDFProcessor
    from config import EXCEL_FILE, PDF_TEMPLATE, OUTPUT_FOLDER
    
    print("Bắt đầu xử lý PDF...")
    print(f"File Excel: {EXCEL_FILE}")
    print(f"PDF mẫu: {PDF_TEMPLATE}")
    print(f"Thư mục output: {OUTPUT_FOLDER}")
    
    # Khởi tạo processor
    processor = PDFProcessor(EXCEL_FILE, PDF_TEMPLATE, OUTPUT_FOLDER)
    
    # Xử lý chỉ 3 file đầu tiên để test
    print("Xử lý 3 file đầu tiên để test...")
    for index, row in processor.data.head(3).iterrows():
        processor.process_single_pdf(row, index)
    
    print("<PERSON><PERSON><PERSON> thành test! <PERSON><PERSON><PERSON> tra thư mục output để xem kết quả.")
    
except Exception as e:
    print(f"Lỗi: {e}")
    import traceback
    traceback.print_exc()
