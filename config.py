# Cấu hình cho việc xử lý PDF

# Đường dẫn file
EXCEL_FILE = "Danh muc Import.V3.xlsx"
PDF_TEMPLATE = "Nhan hop.pdf"
OUTPUT_FOLDER = "output_pdfs"

# Mapping text cần thay thế trong PDF với dữ liệu từ Excel
# Dựa trên nội dung thực tế trong file PDF mẫu
COLUMN_MAPPING = {
    "KTO-2022-01": "ma_ho_so",  # Thay mã hồ sơ mẫu bằng mã thực
    "2022": "nam",  # Thay năm mẫu bằng năm thực
    "E324-A-": "vi_tri",  # Thay vị trí mẫu bằng vị trí thực
    "HỒ SƠ KẾ TOÁN NĂM 2022": "ten_chung_tu",  # Thay tiêu đề
    "31/12/2032": "thoi_gian_luu",  # Thay thời hạn lưu
    # Thêm các mapping khác nếu cần
}

# <PERSON><PERSON>u hình QR Code
QR_CONFIG = {
    "size": (100, 100),  # <PERSON>ích thước QR code (width, height)
    "position": (400, 100, 500, 200),  # Vị trí QR code trong PDF (x, y, width, height)
    "data_column": "ma_ho_so",  # Cột mã hồ sơ để tạo QR code
    "page_number": 0  # Trang để chèn QR code (0 = trang đầu tiên)
}

# Cấu hình logging
LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
