# Cấu hình cho việc xử lý PDF

# Đường dẫn file
EXCEL_FILE = "Danh muc Import.V3.xlsx"
PDF_TEMPLATE = "Nhan hop.pdf"
OUTPUT_FOLDER = "output_pdfs"

# Mapping cột Excel với placeholder trong PDF
# Điều chỉnh theo cấu trúc thực tế của file Excel
COLUMN_MAPPING = {
    "{{TEN}}": "Tên",  # Placeholder trong PDF : Tên cột trong Excel
    "{{MA_VI_TRI}}": "Mã vị trí",
    "{{MA_HO_SO}}": "Mã hồ sơ",
    "{{SO_DIEN_THOAI}}": "Số điện thoại",
    "{{DIA_CHI}}": "Địa chỉ",
    # Thêm các mapping khác nếu cần
}

# Cấu hình QR Code
QR_CONFIG = {
    "size": (100, 100),  # <PERSON>í<PERSON> thước QR code (width, height)
    "position": (400, 100, 500, 200),  # Vị trí QR code trong PDF (x, y, width, height)
    "data_column": "Mã hồ sơ",  # Cột dữ liệu để tạo QR code
    "page_number": 0  # Trang để chèn QR code (0 = trang đầu tiên)
}

# Cấu hình logging
LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
