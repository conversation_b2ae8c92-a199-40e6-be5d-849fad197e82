# Cấu hình cho việc xử lý PDF

# Đường dẫn file
EXCEL_FILE = "Danh muc Import.V3.xlsx"
PDF_TEMPLATE = "Nhan hop.pdf"
OUTPUT_FOLDER = "output_pdfs"

# Mapping cột Excel với placeholder trong PDF
# Dựa trên cấu trúc thực tế của file Excel
COLUMN_MAPPING = {
    "{{VI_TRI}}": "vi_tri",  # Cột vị trí (E324-A-1-1-1)
    "{{TEN_CHUNG_TU}}": "ten_chung_tu",  # Tên chứng từ
    "{{NAM}}": "nam",  # Năm
    "{{MA_HO_SO}}": "ma_ho_so",  # M<PERSON> hồ sơ (KTO2007-1)
    "{{THOI_GIAN_LUU}}": "thoi_gian_luu",  # Thời gian lưu
    # Thêm các mapping khác nếu cần
}

# Cấu hình QR Code
QR_CONFIG = {
    "size": (100, 100),  # Kích thước QR code (width, height)
    "position": (400, 100, 500, 200),  # Vị trí QR code trong PDF (x, y, width, height)
    "data_column": "ma_ho_so",  # Cột mã hồ sơ để tạo QR code
    "page_number": 0  # Trang để chèn QR code (0 = trang đầu tiên)
}

# Cấu hình logging
LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
