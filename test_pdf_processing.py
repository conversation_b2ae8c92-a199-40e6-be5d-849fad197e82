#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import fitz  # PyMuPDF
import qrcode
from PIL import Image
import io
import os
from pathlib import Path

def test_excel_reading():
    """Test đọc file Excel"""
    print("=== TEST ĐỌC FILE EXCEL ===")
    try:
        # Đọc file Excel, bỏ qua dòng đầu tiên
        df = pd.read_excel("Danh muc Import.V3.xlsx", skiprows=1)
        
        # Đặt tên cột
        df.columns = ['vi_tri', 'ten_chung_tu', 'nam', 'ma_ho_so', 'thoi_gian_luu']
        
        # Loại bỏ các dòng trống
        df = df.dropna(subset=['vi_tri', 'ma_ho_so'])
        
        print(f"✓ Đọc thành công {len(df)} dòng dữ liệu")
        print("✓ Dữ liệu mẫu (5 dòng đầu):")
        print(df.head().to_string())
        return df
        
    except Exception as e:
        print(f"✗ Lỗi đọc Excel: {e}")
        return None

def test_pdf_reading():
    """Test đọc file PDF"""
    print("\n=== TEST ĐỌC FILE PDF ===")
    try:
        doc = fitz.open("Nhan hop.pdf")
        print(f"✓ Đọc thành công PDF với {len(doc)} trang")
        
        # Lấy text từ trang đầu tiên
        page = doc[0]
        text = page.get_text()
        print(f"✓ Text từ trang đầu tiên (100 ký tự đầu): {text[:100]}...")
        
        doc.close()
        return True
        
    except Exception as e:
        print(f"✗ Lỗi đọc PDF: {e}")
        return False

def test_qr_generation():
    """Test tạo QR code"""
    print("\n=== TEST TẠO QR CODE ===")
    try:
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data("KTO2007-1")
        qr.make(fit=True)
        
        qr_img = qr.make_image(fill_color="black", back_color="white")
        qr_img = qr_img.resize((100, 100))
        
        # Lưu QR code test
        qr_img.save("test_qr.png")
        print("✓ Tạo QR code thành công và lưu vào test_qr.png")
        return True
        
    except Exception as e:
        print(f"✗ Lỗi tạo QR code: {e}")
        return False

def test_simple_pdf_processing():
    """Test xử lý PDF đơn giản"""
    print("\n=== TEST XỬ LÝ PDF ĐơN GIẢN ===")
    try:
        # Mở PDF
        doc = fitz.open("Nhan hop.pdf")
        
        # Tạo QR code
        qr = qrcode.QRCode(version=1, error_correction=qrcode.constants.ERROR_CORRECT_L, box_size=10, border=4)
        qr.add_data("TEST-QR-CODE")
        qr.make(fit=True)
        qr_img = qr.make_image(fill_color="black", back_color="white")
        qr_img = qr_img.resize((100, 100))
        
        # Chuyển PIL Image thành bytes
        img_buffer = io.BytesIO()
        qr_img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        # Chèn QR code vào PDF
        page = doc[0]
        rect = fitz.Rect(400, 100, 500, 200)  # x1, y1, x2, y2
        page.insert_image(rect, stream=img_buffer.getvalue())
        
        # Lưu PDF test
        output_path = "test_output.pdf"
        doc.save(output_path)
        doc.close()
        
        print(f"✓ Xử lý PDF thành công và lưu vào {output_path}")
        return True
        
    except Exception as e:
        print(f"✗ Lỗi xử lý PDF: {e}")
        return False

def main():
    """Chạy tất cả các test"""
    print("KIỂM TRA CÁC THÀNH PHẦN CỦA HỆ THỐNG XỬ LÝ PDF")
    print("=" * 50)
    
    # Test các thành phần
    excel_ok = test_excel_reading() is not None
    pdf_ok = test_pdf_reading()
    qr_ok = test_qr_generation()
    processing_ok = test_simple_pdf_processing()
    
    print("\n" + "=" * 50)
    print("KẾT QUẢ KIỂM TRA:")
    print(f"Đọc Excel: {'✓' if excel_ok else '✗'}")
    print(f"Đọc PDF: {'✓' if pdf_ok else '✗'}")
    print(f"Tạo QR Code: {'✓' if qr_ok else '✗'}")
    print(f"Xử lý PDF: {'✓' if processing_ok else '✗'}")
    
    if all([excel_ok, pdf_ok, qr_ok, processing_ok]):
        print("\n🎉 TẤT CẢ KIỂM TRA THÀNH CÔNG! Hệ thống sẵn sàng hoạt động.")
    else:
        print("\n⚠️  CÓ LỖI XẢY RA. Vui lòng kiểm tra lại.")

if __name__ == "__main__":
    main()
