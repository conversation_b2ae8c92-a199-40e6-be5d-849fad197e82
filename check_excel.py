import pandas as pd
import sys

def check_excel_structure(excel_file):
    """Kiểm tra cấu trúc file Excel"""
    try:
        # Đọc file Excel
        df = pd.read_excel(excel_file)
        
        print(f"=== THÔNG TIN FILE EXCEL: {excel_file} ===")
        print(f"Số dòng dữ liệu: {len(df)}")
        print(f"Số cột: {len(df.columns)}")
        print("\n=== CÁC CỘT TRONG FILE ===")
        for i, col in enumerate(df.columns, 1):
            print(f"{i}. {col}")
        
        print("\n=== MẪU DỮ LIỆU (5 DÒNG ĐẦU) ===")
        print(df.head().to_string())
        
        print("\n=== KIỂU DỮ LIỆU CỦA TỪNG CỘT ===")
        print(df.dtypes)
        
        print("\n=== THỐNG KÊ DỮ LIỆU THIẾU ===")
        missing_data = df.isnull().sum()
        for col, missing_count in missing_data.items():
            if missing_count > 0:
                print(f"{col}: {missing_count} dòng thiếu dữ liệu")
        
        return df
        
    except Exception as e:
        print(f"Lỗi khi đọc file Excel: {e}")
        return None

if __name__ == "__main__":
    excel_file = "Danh muc Import.V3.xlsx"
    check_excel_structure(excel_file)
