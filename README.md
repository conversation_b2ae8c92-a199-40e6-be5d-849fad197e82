# Hướng dẫn sử dụng công cụ xử lý PDF

## Mô tả
Công cụ này giúp xử lý file PDF bằng cách thay thế thông tin (tên, mã vị trí, mã hồ sơ) và chèn mã QR theo dữ liệu từ file Excel.

## Cài đặt

### 1. Cài đặt Python
Đảm bảo bạn đã cài đặt Python 3.7 trở lên.

### 2. Cài đặt thư viện
```bash
pip install -r requirements.txt
```

## Cấu hình

### 1. Kiểm tra cấu trúc file Excel
Trước tiên, chạy script kiểm tra để xem cấu trúc file Excel:
```bash
python check_excel.py
```

### 2. Điều chỉnh cấu hình
Mở file `config.py` và điều chỉnh các thông số:

- **COLUMN_MAPPING**: Mapping giữa placeholder trong PDF và tên cột trong Excel
- **QR_CONFIG**: <PERSON><PERSON><PERSON> hình cho mã QR (<PERSON><PERSON><PERSON>, vị tr<PERSON>, d<PERSON> li<PERSON>)
- **<PERSON>ường dẫn file**: Excel, PDF mẫu, thư mục output

### 3. Chuẩn bị file PDF mẫu
Trong file PDF mẫu, sử dụng các placeholder như:
- `{{TEN}}` - sẽ được thay thế bằng tên
- `{{MA_VI_TRI}}` - sẽ được thay thế bằng mã vị trí  
- `{{MA_HO_SO}}` - sẽ được thay thế bằng mã hồ sơ

## Sử dụng

### Chạy chương trình chính
```bash
python xu_ly_pdf.py
```

## Cấu trúc file

- `xu_ly_pdf.py`: File chính chứa logic xử lý
- `config.py`: File cấu hình các tham số
- `check_excel.py`: Script kiểm tra cấu trúc Excel
- `requirements.txt`: Danh sách thư viện cần thiết
- `Danh muc Import.V3.xlsx`: File Excel chứa dữ liệu
- `Nhan hop.pdf`: File PDF mẫu
- `output_pdfs/`: Thư mục chứa file PDF đã xử lý

## Lưu ý

1. **Placeholder trong PDF**: Đảm bảo file PDF mẫu có các placeholder đúng định dạng
2. **Tên cột Excel**: Kiểm tra tên cột trong Excel khớp với cấu hình
3. **Vị trí QR code**: Điều chỉnh vị trí QR code trong `config.py` cho phù hợp
4. **Encoding**: Đảm bảo file Excel được lưu với encoding UTF-8

## Xử lý lỗi

- Kiểm tra log để xem chi tiết lỗi
- Đảm bảo file Excel và PDF mẫu tồn tại
- Kiểm tra quyền ghi vào thư mục output
- Xác nhận tên cột trong Excel khớp với cấu hình

## Ví dụ cấu hình

```python
# Trong config.py
COLUMN_MAPPING = {
    "{{TEN}}": "Họ và tên",
    "{{MA_VI_TRI}}": "Mã vị trí",
    "{{MA_HO_SO}}": "Mã hồ sơ",
}

QR_CONFIG = {
    "size": (100, 100),
    "position": (400, 100, 500, 200),
    "data_column": "Mã hồ sơ",
    "page_number": 0
}
```
