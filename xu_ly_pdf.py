import pandas as pd
import fitz  # PyMuPDF
import qrcode
from PIL import Image
import io
import os
from pathlib import Path
import logging
from config import EXCEL_FILE, PDF_TEMPLATE, OUTPUT_FOLDER, COLUMN_MAPPING, QR_CONFIG, LOG_LEVEL, LOG_FORMAT

# Thiết lập logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

class PDFProcessor:
    def __init__(self, excel_file, pdf_template, output_folder="output"):
        """
        Khởi tạo PDFProcessor
        
        Args:
            excel_file (str): Đường dẫn đến file Excel chứa dữ liệu
            pdf_template (str): Đường dẫn đến file PDF mẫu
            output_folder (str): Thư mục lưu file PDF đã xử lý
        """
        self.excel_file = excel_file
        self.pdf_template = pdf_template
        self.output_folder = output_folder
        
        # Tạo thư mục output nếu chưa tồn tại
        Path(self.output_folder).mkdir(exist_ok=True)
        
        # Đọc dữ liệu từ Excel
        self.data = self.load_excel_data()
        
    def load_excel_data(self):
        """Đọc dữ liệu từ file Excel"""
        try:
            # Đọc file Excel, bỏ qua dòng đầu tiên (header)
            df = pd.read_excel(self.excel_file, skiprows=1)

            # Đặt tên cột cho dễ hiểu
            df.columns = ['vi_tri', 'ten_chung_tu', 'nam', 'ma_ho_so', 'thoi_gian_luu']

            # Loại bỏ các dòng trống
            df = df.dropna(subset=['vi_tri', 'ma_ho_so'])

            logger.info(f"Đã đọc {len(df)} dòng dữ liệu từ {self.excel_file}")
            logger.info(f"Các cột trong file Excel: {list(df.columns)}")
            return df
        except Exception as e:
            logger.error(f"Lỗi khi đọc file Excel: {e}")
            raise
    
    def generate_qr_code(self, data, size=(100, 100)):
        """
        Tạo mã QR từ dữ liệu
        
        Args:
            data (str): Dữ liệu để tạo QR code
            size (tuple): Kích thước QR code
            
        Returns:
            PIL.Image: Ảnh QR code
        """
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(data)
        qr.make(fit=True)
        
        qr_img = qr.make_image(fill_color="black", back_color="white")
        qr_img = qr_img.resize(size)
        return qr_img
    
    def replace_text_in_pdf(self, doc, old_text, new_text):
        """
        Thay thế text trong PDF

        Args:
            doc: PyMuPDF document object
            old_text (str): Text cần thay thế
            new_text (str): Text mới
        """
        for page_num in range(len(doc)):
            page = doc[page_num]
            text_instances = page.search_for(old_text)

            for inst in text_instances:
                # Tạo annotation để thay thế text
                page.add_redact_annot(inst, text=new_text)

        # Áp dụng các thay đổi (sử dụng phương thức đúng)
        for page_num in range(len(doc)):
            page = doc[page_num]
            page.apply_redactions()
    
    def insert_qr_code(self, doc, qr_image, position, page_num=0):
        """
        Chèn QR code vào PDF
        
        Args:
            doc: PyMuPDF document object
            qr_image: PIL Image object của QR code
            position (tuple): Vị trí (x, y, width, height) để chèn QR code
            page_num (int): Số trang để chèn QR code
        """
        page = doc[page_num]
        
        # Chuyển PIL Image thành bytes
        img_buffer = io.BytesIO()
        qr_image.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        # Chèn ảnh vào PDF
        rect = fitz.Rect(position)
        page.insert_image(rect, stream=img_buffer.getvalue())
    
    def process_single_pdf(self, row_data, row_index):
        """
        Xử lý một file PDF cho một dòng dữ liệu

        Args:
            row_data: Dữ liệu của một dòng từ Excel
            row_index (int): Chỉ số dòng
        """
        try:
            # Mở file PDF mẫu
            doc = fitz.open(self.pdf_template)

            # Thay thế các thông tin theo cấu hình
            replacements = {}
            for placeholder, column_name in COLUMN_MAPPING.items():
                replacements[placeholder] = str(row_data.get(column_name, ''))

            # Thực hiện thay thế text
            for old_text, new_text in replacements.items():
                self.replace_text_in_pdf(doc, old_text, new_text)

            # Tạo QR code
            qr_data = row_data.get(QR_CONFIG['data_column'], f'Row_{row_index}')
            qr_image = self.generate_qr_code(qr_data, QR_CONFIG['size'])

            # Chèn QR code
            self.insert_qr_code(doc, qr_image, QR_CONFIG['position'], QR_CONFIG['page_number'])

            # Lưu file PDF mới
            output_filename = f"processed_{row_index + 1}_{qr_data}.pdf"
            output_path = os.path.join(self.output_folder, output_filename)
            doc.save(output_path)
            doc.close()

            logger.info(f"Đã xử lý thành công file: {output_filename}")

        except Exception as e:
            logger.error(f"Lỗi khi xử lý dòng {row_index + 1}: {e}")
    
    def process_all(self):
        """Xử lý tất cả các dòng trong Excel"""
        logger.info("Bắt đầu xử lý tất cả các file PDF...")
        
        for index, row in self.data.iterrows():
            self.process_single_pdf(row, index)
        
        logger.info(f"Hoàn thành xử lý {len(self.data)} file PDF")

def main():
    """Hàm main để chạy chương trình"""
    try:
        # Khởi tạo processor với cấu hình từ config.py
        processor = PDFProcessor(EXCEL_FILE, PDF_TEMPLATE, OUTPUT_FOLDER)

        # Xử lý tất cả file
        processor.process_all()

        print(f"Xử lý hoàn tất! Kiểm tra thư mục {OUTPUT_FOLDER} để xem kết quả.")

    except Exception as e:
        logger.error(f"Lỗi trong quá trình xử lý: {e}")
        print(f"Có lỗi xảy ra: {e}")

if __name__ == "__main__":
    main()
