# HƯỚNG DẪN SỬ DỤNG CÔNG CỤ XỬ LÝ PDF

## 📋 Mô tả
Công cụ này sẽ:
1. Đ<PERSON><PERSON> dữ liệu từ file Excel "Danh muc Import.V3.xlsx"
2. Với mỗi dòng dữ liệu, tạo một file PDF mới từ file mẫu "Nhan hop.pdf"
3. Thay thế các placeholder trong PDF bằng dữ liệu thực
4. Chèn mã QR vào PDF
5. Lưu file PDF đã xử lý vào thư mục output

## 🚀 Cách sử dụng

### Bước 1: Chuẩn bị file PDF mẫu
Trong file "Nhan hop.pdf", thêm các placeholder sau:
- `{{VI_TRI}}` - sẽ được thay thế bằng vị trí (ví dụ: E324-A-1-1-1)
- `{{TEN_CHUNG_TU}}` - sẽ được thay thế bằng tên chứng từ
- `{{NAM}}` - sẽ được thay thế bằng năm
- `{{MA_HO_SO}}` - sẽ được thay thế bằng mã hồ sơ
- `{{THOI_GIAN_LUU}}` - sẽ được thay thế bằng thời gian lưu

### Bước 2: Chạy test
```bash
python test_pdf_processing.py
```

### Bước 3: Chạy xử lý (test với 3 file đầu tiên)
```bash
python run_processing.py
```

### Bước 4: Chạy xử lý toàn bộ
```bash
python xu_ly_pdf.py
```

## ⚙️ Cấu hình

### Điều chỉnh vị trí QR code
Mở file `config.py` và thay đổi:
```python
QR_CONFIG = {
    "size": (100, 100),  # Kích thước QR code
    "position": (400, 100, 500, 200),  # Vị trí (x1, y1, x2, y2)
    "data_column": "ma_ho_so",  # Dữ liệu cho QR code
    "page_number": 0  # Trang chèn QR code
}
```

### Thay đổi placeholder
Trong `config.py`, điều chỉnh:
```python
COLUMN_MAPPING = {
    "{{VI_TRI}}": "vi_tri",
    "{{TEN_CHUNG_TU}}": "ten_chung_tu",
    # ... thêm mapping khác
}
```

## 📁 Cấu trúc file

```
📁 Thư mục dự án/
├── 📄 Danh muc Import.V3.xlsx    # File Excel dữ liệu đầu vào
├── 📄 Nhan hop.pdf               # File PDF mẫu
├── 📄 xu_ly_pdf.py              # Script chính
├── 📄 config.py                 # File cấu hình
├── 📄 run_processing.py         # Script chạy test
├── 📄 test_pdf_processing.py    # Script kiểm tra hệ thống
├── 📄 check_excel.py            # Script kiểm tra Excel
├── 📄 requirements.txt          # Danh sách thư viện
└── 📁 output_pdfs/              # Thư mục chứa file PDF đã xử lý
```

## 🔧 Xử lý lỗi thường gặp

### Lỗi "File not found"
- Kiểm tra file Excel và PDF có tồn tại không
- Đảm bảo tên file chính xác

### Lỗi "Permission denied"
- Đóng file Excel nếu đang mở
- Chạy với quyền administrator nếu cần

### QR code không hiển thị đúng vị trí
- Điều chỉnh `position` trong `config.py`
- Tọa độ (x1, y1, x2, y2) tính từ góc trái trên

### Placeholder không được thay thế
- Kiểm tra placeholder trong PDF có đúng format không
- Đảm bảo mapping trong `config.py` chính xác

## 📊 Dữ liệu Excel

File Excel có cấu trúc:
- Cột 1: Vị trí (E324-A-1-1-1)
- Cột 2: Tên chứng từ
- Cột 3: Năm
- Cột 4: Mã hồ sơ (KTO2007-1)
- Cột 5: Thời gian lưu

Tổng cộng: 523 dòng dữ liệu

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Chạy `python check_excel.py` để kiểm tra dữ liệu
2. Chạy `python test_pdf_processing.py` để kiểm tra hệ thống
3. Kiểm tra log để xem chi tiết lỗi
