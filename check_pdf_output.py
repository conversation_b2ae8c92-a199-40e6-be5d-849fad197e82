#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import fitz  # PyMuPDF
import os

def check_pdf_content(pdf_path):
    """Kiểm tra nội dung file PDF"""
    try:
        print(f"\n=== KIỂM TRA FILE: {pdf_path} ===")
        
        # Mở file PDF
        doc = fitz.open(pdf_path)
        print(f"✓ Số trang: {len(doc)}")
        
        # Lấy text từ trang đầu tiên
        page = doc[0]
        text = page.get_text()
        print(f"✓ Nội dung text:")
        print("-" * 50)
        print(text)
        print("-" * 50)
        
        # Kiểm tra xem có ảnh (QR code) không
        image_list = page.get_images()
        print(f"✓ Số lượng ảnh trong trang: {len(image_list)}")
        
        doc.close()
        return True
        
    except Exception as e:
        print(f"✗ Lỗi khi đọc file {pdf_path}: {e}")
        return False

def compare_with_original():
    """So sánh với file gốc"""
    print("=== SO SÁNH VỚI FILE GỐC ===")
    
    # Kiểm tra file gốc
    original_file = "Nhan hop.pdf"
    if os.path.exists(original_file):
        print(f"\n--- FILE GỐC: {original_file} ---")
        check_pdf_content(original_file)
    
    # Kiểm tra các file đã xử lý
    output_folder = "output_pdfs"
    if os.path.exists(output_folder):
        pdf_files = [f for f in os.listdir(output_folder) if f.endswith('.pdf')]
        
        for pdf_file in pdf_files[:3]:  # Kiểm tra 3 file đầu tiên
            pdf_path = os.path.join(output_folder, pdf_file)
            check_pdf_content(pdf_path)

def main():
    """Chạy kiểm tra"""
    print("KIỂM TRA NỘI DUNG FILE PDF ĐÃ XỬ LÝ")
    print("=" * 60)
    
    compare_with_original()

if __name__ == "__main__":
    main()
