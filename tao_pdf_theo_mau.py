#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import qrcode
from PIL import Image
import io
import os
from pathlib import Path
import logging
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import mm
from reportlab.lib.colors import Color

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PDFGenerator:
    def __init__(self, excel_file, output_folder="output_pdfs"):
        """
        Khởi tạo PDFGenerator
        
        Args:
            excel_file (str): Đường dẫn đến file Excel chứa dữ liệu
            output_folder (str): Thư mục lưu file PDF đã tạo
        """
        self.excel_file = excel_file
        self.output_folder = output_folder
        
        # Tạo thư mục output nếu chưa tồn tại
        Path(self.output_folder).mkdir(exist_ok=True)
        
        # Đọc dữ liệu từ Excel
        self.data = self.load_excel_data()
        
        # Thiết lập font (sử dụng font mặc định của ReportLab)
        self.setup_fonts()
        
    def load_excel_data(self):
        """Đọc dữ liệu từ file Excel"""
        try:
            # Đọc file Excel, bỏ qua dòng đầu tiên (header)
            df = pd.read_excel(self.excel_file, skiprows=1)
            
            # Đặt tên cột cho dễ hiểu
            df.columns = ['vi_tri', 'ten_chung_tu', 'nam', 'ma_ho_so', 'thoi_gian_luu']
            
            # Loại bỏ các dòng trống
            df = df.dropna(subset=['vi_tri', 'ma_ho_so'])
            
            logger.info(f"Đã đọc {len(df)} dòng dữ liệu từ {self.excel_file}")
            return df
        except Exception as e:
            logger.error(f"Lỗi khi đọc file Excel: {e}")
            raise
    
    def setup_fonts(self):
        """Thiết lập font cho PDF"""
        # Sử dụng font mặc định của ReportLab hỗ trợ Unicode
        self.title_font = "Helvetica-Bold"
        self.normal_font = "Helvetica"
        self.title_size = 24
        self.subtitle_size = 20
        self.normal_size = 14
        
    def generate_qr_code(self, data, size=(80, 80)):
        """
        Tạo mã QR
        
        Args:
            data (str): Dữ liệu để tạo QR code
            size (tuple): Kích thước QR code
            
        Returns:
            PIL.Image: Ảnh QR code
        """
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(data)
        qr.make(fit=True)
        
        qr_img = qr.make_image(fill_color="black", back_color="white")
        qr_img = qr_img.resize(size)
        return qr_img
    
    def create_pdf_template(self, canvas_obj, row_data):
        """
        Tạo PDF theo mẫu
        
        Args:
            canvas_obj: Canvas object của ReportLab
            row_data: Dữ liệu của một dòng từ Excel
        """
        # Kích thước trang A4
        width, height = A4
        
        # Màu xanh dương cho tiêu đề
        blue_color = Color(46/255, 49/255, 146/255)  # RGB(46, 49, 146)
        
        # Vẽ khung viền
        canvas_obj.setStrokeColor(blue_color)
        canvas_obj.setLineWidth(3)
        canvas_obj.rect(20*mm, 20*mm, width-40*mm, height-40*mm)
        
        # Logo MEDLATEC (vẽ hình tròn vàng đơn giản)
        canvas_obj.setFillColor(Color(1, 0.8, 0))  # Màu vàng
        canvas_obj.circle(60*mm, height-60*mm, 15*mm, fill=1)
        
        # Text "MEDLATEC" trong logo
        canvas_obj.setFillColor(blue_color)
        canvas_obj.setFont(self.title_font, 8)
        text_width = canvas_obj.stringWidth("MEDLATEC", self.title_font, 8)
        canvas_obj.drawString(60*mm - text_width/2, height-63*mm, "MEDLATEC")

        # Tiêu đề chính
        canvas_obj.setFont(self.title_font, self.title_size)
        canvas_obj.setFillColor(blue_color)
        title_text = "PHONG KE TOAN MEDLATEC"
        text_width = canvas_obj.stringWidth(title_text, self.title_font, self.title_size)
        canvas_obj.drawString(width/2 - text_width/2, height-80*mm, title_text)

        # Vẽ đường kẻ ngang
        canvas_obj.setStrokeColor(blue_color)
        canvas_obj.setLineWidth(2)
        canvas_obj.line(80*mm, height-90*mm, width-80*mm, height-90*mm)

        # Tiêu đề hồ sơ
        canvas_obj.setFont(self.title_font, self.subtitle_size)
        canvas_obj.setFillColor(blue_color)

        # Tạo tiêu đề dựa trên dữ liệu
        ho_so_title = f"HO SO: {row_data.get('ten_chung_tu', '')}"
        nam_title = f"NAM {row_data.get('nam', '')}"

        # Vẽ tiêu đề hồ sơ (căn giữa)
        text_width = canvas_obj.stringWidth(ho_so_title, self.title_font, self.subtitle_size)
        canvas_obj.drawString(width/2 - text_width/2, height-120*mm, ho_so_title)

        text_width = canvas_obj.stringWidth(nam_title, self.title_font, self.subtitle_size)
        canvas_obj.drawString(width/2 - text_width/2, height-140*mm, nam_title)

        # Thông tin chi tiết
        canvas_obj.setFont(self.normal_font, self.normal_size)
        canvas_obj.setFillColor(blue_color)

        # Thời hạn lưu
        thoi_han_text = f"Thoi han luu: {row_data.get('thoi_gian_luu', '')}"
        text_width = canvas_obj.stringWidth(thoi_han_text, self.normal_font, self.normal_size)
        canvas_obj.drawString(width/2 - text_width/2, height-170*mm, thoi_han_text)

        # Mã hồ sơ
        ma_ho_so_text = f"Ma ho so: {row_data.get('ma_ho_so', '')}"
        text_width = canvas_obj.stringWidth(ma_ho_so_text, self.normal_font, self.normal_size)
        canvas_obj.drawString(width/2 - text_width/2, height-185*mm, ma_ho_so_text)
        
        # Vị trí (góc dưới bên trái)
        vi_tri_text = str(row_data.get('vi_tri', ''))
        canvas_obj.setFont(self.title_font, 16)
        canvas_obj.drawString(40*mm, 40*mm, vi_tri_text)
        
        # Tạo và chèn QR code
        qr_data = row_data.get('ma_ho_so', '')
        if qr_data:
            try:
                qr_img = self.generate_qr_code(qr_data)

                # Lưu QR code tạm thời vào file
                temp_qr_path = f"temp_qr_{qr_data}.png"
                qr_img.save(temp_qr_path)

                # Chèn QR code vào PDF
                canvas_obj.drawInlineImage(temp_qr_path, 40*mm, height-200*mm, width=25*mm, height=25*mm)

                # Xóa file tạm
                os.remove(temp_qr_path)

            except Exception as e:
                logger.warning(f"Không thể tạo QR code: {e}")
                # Fallback: vẽ text thay thế
                canvas_obj.setFont(self.normal_font, 10)
                canvas_obj.drawString(40*mm, height-200*mm, f"QR: {qr_data}")
        
    def create_single_pdf(self, row_data, row_index):
        """
        Tạo một file PDF cho một dòng dữ liệu
        
        Args:
            row_data: Dữ liệu của một dòng từ Excel
            row_index (int): Chỉ số dòng
        """
        try:
            # Tạo tên file output
            ma_ho_so = row_data.get('ma_ho_so', f'Row_{row_index}')
            output_filename = f"ho_so_{row_index + 1}_{ma_ho_so}.pdf"
            output_path = os.path.join(self.output_folder, output_filename)
            
            # Tạo PDF
            c = canvas.Canvas(output_path, pagesize=A4)
            
            # Vẽ nội dung PDF
            self.create_pdf_template(c, row_data)
            
            # Lưu file
            c.save()
            
            logger.info(f"Đã tạo thành công file: {output_filename}")
            
        except Exception as e:
            logger.error(f"Lỗi khi tạo PDF cho dòng {row_index + 1}: {e}")
    
    def create_all_pdfs(self):
        """Tạo tất cả các file PDF"""
        logger.info("Bắt đầu tạo tất cả các file PDF...")
        
        for index, row in self.data.iterrows():
            self.create_single_pdf(row, index)
        
        logger.info(f"Hoàn thành tạo {len(self.data)} file PDF")

def main():
    """Hàm main để chạy chương trình"""
    try:
        # Khởi tạo generator
        generator = PDFGenerator("Danh muc Import.V3.xlsx", "output_pdfs")
        
        # Tạo tất cả file PDF
        generator.create_all_pdfs()
        
        print(f"Tạo PDF hoàn tất! Kiểm tra thư mục output_pdfs để xem kết quả.")
        
    except Exception as e:
        logger.error(f"Lỗi trong quá trình tạo PDF: {e}")
        print(f"Có lỗi xảy ra: {e}")

if __name__ == "__main__":
    main()
