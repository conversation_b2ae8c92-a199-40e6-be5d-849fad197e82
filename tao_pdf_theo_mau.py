#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import qrcode
from PIL import Image
import io
import os
from pathlib import Path
import logging
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import mm
from reportlab.lib.colors import Color

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PDFGenerator:
    def __init__(self, excel_file, output_folder="output_pdfs"):
        """
        Khởi tạo PDFGenerator
        
        Args:
            excel_file (str): Đường dẫn đến file Excel chứa dữ liệu
            output_folder (str): Thư mục lưu file PDF đã tạo
        """
        self.excel_file = excel_file
        self.output_folder = output_folder
        
        # Tạo thư mục output nếu chưa tồn tại
        Path(self.output_folder).mkdir(exist_ok=True)
        
        # Đọc dữ liệu từ Excel
        self.data = self.load_excel_data()
        
        # Thiết lập font (sử dụng font mặc định của ReportLab)
        self.setup_fonts()
        
    def load_excel_data(self):
        """Đọc dữ liệu từ file Excel"""
        try:
            # Đọc file Excel, bỏ qua dòng đầu tiên (header)
            df = pd.read_excel(self.excel_file, skiprows=1)
            
            # Đặt tên cột cho dễ hiểu
            df.columns = ['vi_tri', 'ten_chung_tu', 'nam', 'ma_ho_so', 'thoi_gian_luu']
            
            # Loại bỏ các dòng trống
            df = df.dropna(subset=['vi_tri', 'ma_ho_so'])
            
            logger.info(f"Đã đọc {len(df)} dòng dữ liệu từ {self.excel_file}")
            return df
        except Exception as e:
            logger.error(f"Lỗi khi đọc file Excel: {e}")
            raise
    
    def setup_fonts(self):
        """Thiết lập font cho PDF"""
        # Sử dụng font mặc định của ReportLab hỗ trợ Unicode
        self.title_font = "Helvetica-Bold"
        self.normal_font = "Helvetica"
        self.title_size = 24
        self.subtitle_size = 20
        self.normal_size = 14
        
    def generate_qr_code(self, data, size=(80, 80)):
        """
        Tạo mã QR
        
        Args:
            data (str): Dữ liệu để tạo QR code
            size (tuple): Kích thước QR code
            
        Returns:
            PIL.Image: Ảnh QR code
        """
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(data)
        qr.make(fit=True)
        
        qr_img = qr.make_image(fill_color="black", back_color="white")
        qr_img = qr_img.resize(size)
        return qr_img
    
    def create_pdf_template(self, canvas_obj, row_data):
        """
        Tạo PDF theo mẫu - nhãn hộp ở giữa trang

        Args:
            canvas_obj: Canvas object của ReportLab
            row_data: Dữ liệu của một dòng từ Excel
        """
        # Kích thước trang A4
        width, height = A4

        # Kích thước nhãn hộp (khoảng 150mm x 100mm)
        label_width = 150*mm
        label_height = 100*mm

        # Vị trí nhãn ở giữa trang
        label_x = (width - label_width) / 2
        label_y = (height - label_height) / 2

        # Màu xanh dương cho tiêu đề
        blue_color = Color(46/255, 49/255, 146/255)  # RGB(46, 49, 146)

        # Vẽ khung viền nhãn (màu đen, dày)
        canvas_obj.setStrokeColor(Color(0, 0, 0))  # Màu đen
        canvas_obj.setLineWidth(2)
        canvas_obj.rect(label_x, label_y, label_width, label_height)
        
        # Logo MEDLATEC (vẽ hình tròn vàng ở góc trên trái nhãn)
        logo_x = label_x + 15*mm
        logo_y = label_y + label_height - 20*mm

        canvas_obj.setFillColor(Color(1, 0.8, 0))  # Màu vàng
        canvas_obj.circle(logo_x, logo_y, 8*mm, fill=1)

        # Text "MEDLATEC" trong logo
        canvas_obj.setFillColor(blue_color)
        canvas_obj.setFont(self.normal_font, 6)
        text_width = canvas_obj.stringWidth("MEDLATEC", self.normal_font, 6)
        canvas_obj.drawString(logo_x - text_width/2, logo_y - 2*mm, "MEDLATEC")

        # Tiêu đề chính (trong nhãn)
        canvas_obj.setFont(self.title_font, 14)
        canvas_obj.setFillColor(blue_color)
        title_text = "PHONG KE TOAN MEDLATEC"
        text_width = canvas_obj.stringWidth(title_text, self.title_font, 14)
        title_x = label_x + (label_width - text_width) / 2
        title_y = label_y + label_height - 15*mm
        canvas_obj.drawString(title_x, title_y, title_text)

        # Vẽ đường kẻ ngang (trong nhãn)
        canvas_obj.setStrokeColor(blue_color)
        canvas_obj.setLineWidth(1)
        line_margin = 10*mm
        canvas_obj.line(label_x + line_margin, title_y - 5*mm,
                       label_x + label_width - line_margin, title_y - 5*mm)

        # Tiêu đề hồ sơ (trong nhãn)
        canvas_obj.setFont(self.title_font, 12)
        canvas_obj.setFillColor(blue_color)

        # Tạo tiêu đề dựa trên dữ liệu
        ho_so_title = f"HO SO KE TOAN NAM {row_data.get('nam', '')}"

        # Vẽ tiêu đề hồ sơ (căn giữa trong nhãn)
        text_width = canvas_obj.stringWidth(ho_so_title, self.title_font, 12)
        ho_so_x = label_x + (label_width - text_width) / 2
        ho_so_y = title_y - 20*mm
        canvas_obj.drawString(ho_so_x, ho_so_y, ho_so_title)

        # Thông tin chi tiết (bên phải QR code)
        canvas_obj.setFont(self.normal_font, 10)
        canvas_obj.setFillColor(blue_color)

        info_x = label_x + 50*mm  # Bên phải QR code

        # Thời hạn lưu
        thoi_han_text = f"Thoi han luu: {row_data.get('thoi_gian_luu', '')}"
        canvas_obj.drawString(info_x, ho_so_y - 15*mm, thoi_han_text)

        # Mã hồ sơ
        ma_ho_so_text = f"Ma ho so: {row_data.get('ma_ho_so', '')}"
        canvas_obj.drawString(info_x, ho_so_y - 25*mm, ma_ho_so_text)
        
        # Vị trí (góc dưới trái của nhãn)
        vi_tri_text = str(row_data.get('vi_tri', ''))
        canvas_obj.setFont(self.title_font, 10)
        canvas_obj.setFillColor(blue_color)
        canvas_obj.drawString(label_x + 5*mm, label_y + 5*mm, vi_tri_text)

        # Tạo và chèn QR code (bên trái trong nhãn)
        qr_data = row_data.get('ma_ho_so', '')
        if qr_data:
            try:
                qr_img = self.generate_qr_code(qr_data, size=(60, 60))  # QR nhỏ hơn

                # Lưu QR code tạm thời vào file
                temp_qr_path = f"temp_qr_{qr_data}.png"
                qr_img.save(temp_qr_path)

                # Chèn QR code vào PDF (bên trái trong nhãn)
                qr_x = label_x + 10*mm
                qr_y = ho_so_y - 35*mm
                canvas_obj.drawInlineImage(temp_qr_path, qr_x, qr_y, width=20*mm, height=20*mm)

                # Xóa file tạm
                os.remove(temp_qr_path)

            except Exception as e:
                logger.warning(f"Không thể tạo QR code: {e}")
                # Fallback: vẽ text thay thế
                canvas_obj.setFont(self.normal_font, 8)
                canvas_obj.drawString(label_x + 10*mm, ho_so_y - 35*mm, f"QR: {qr_data}")
        
    def create_single_pdf(self, row_data, row_index):
        """
        Tạo một file PDF cho một dòng dữ liệu
        
        Args:
            row_data: Dữ liệu của một dòng từ Excel
            row_index (int): Chỉ số dòng
        """
        try:
            # Tạo tên file output
            ma_ho_so = row_data.get('ma_ho_so', f'Row_{row_index}')
            output_filename = f"ho_so_{row_index + 1}_{ma_ho_so}.pdf"
            output_path = os.path.join(self.output_folder, output_filename)
            
            # Tạo PDF
            c = canvas.Canvas(output_path, pagesize=A4)
            
            # Vẽ nội dung PDF
            self.create_pdf_template(c, row_data)
            
            # Lưu file
            c.save()
            
            logger.info(f"Đã tạo thành công file: {output_filename}")
            
        except Exception as e:
            logger.error(f"Lỗi khi tạo PDF cho dòng {row_index + 1}: {e}")
    
    def create_all_pdfs(self):
        """Tạo tất cả các file PDF"""
        logger.info("Bắt đầu tạo tất cả các file PDF...")
        
        for index, row in self.data.iterrows():
            self.create_single_pdf(row, index)
        
        logger.info(f"Hoàn thành tạo {len(self.data)} file PDF")

def main():
    """Hàm main để chạy chương trình"""
    try:
        # Khởi tạo generator
        generator = PDFGenerator("Danh muc Import.V3.xlsx", "output_pdfs")
        
        # Tạo tất cả file PDF
        generator.create_all_pdfs()
        
        print(f"Tạo PDF hoàn tất! Kiểm tra thư mục output_pdfs để xem kết quả.")
        
    except Exception as e:
        logger.error(f"Lỗi trong quá trình tạo PDF: {e}")
        print(f"Có lỗi xảy ra: {e}")

if __name__ == "__main__":
    main()
